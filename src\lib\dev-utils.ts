// Development utilities and debugging helpers

// Environment detection
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';
export const isTest = process.env.NODE_ENV === 'test';
export const isClient = typeof window !== 'undefined';
export const isServer = typeof window === 'undefined';

// Debug logging with levels
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4,
}

class Logger {
  private level: LogLevel;
  private prefix: string;

  constructor(prefix: string = 'App', level: LogLevel = LogLevel.INFO) {
    this.prefix = prefix;
    this.level = isDevelopment ? LogLevel.DEBUG : level;
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.level;
  }

  private formatMessage(level: string, message: string, ...args: unknown[]): void {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [${this.prefix}] [${level}] ${message}`;
    
    switch (level) {
      case 'ERROR':
        console.error(formattedMessage, ...args);
        break;
      case 'WARN':
        console.warn(formattedMessage, ...args);
        break;
      case 'INFO':
        console.info(formattedMessage, ...args);
        break;
      case 'DEBUG':
        console.debug(formattedMessage, ...args);
        break;
      case 'TRACE':
        console.trace(formattedMessage, ...args);
        break;
      default:
        console.log(formattedMessage, ...args);
    }
  }

  error(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.formatMessage('ERROR', message, ...args);
    }
  }

  warn(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.formatMessage('WARN', message, ...args);
    }
  }

  info(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.formatMessage('INFO', message, ...args);
    }
  }

  debug(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.formatMessage('DEBUG', message, ...args);
    }
  }

  trace(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.TRACE)) {
      this.formatMessage('TRACE', message, ...args);
    }
  }

  group(label: string): void {
    if (isDevelopment) {
      console.group(`[${this.prefix}] ${label}`);
    }
  }

  groupEnd(): void {
    if (isDevelopment) {
      console.groupEnd();
    }
  }

  table(data: unknown): void {
    if (isDevelopment && console.table) {
      console.table(data);
    }
  }

  time(label: string): void {
    if (isDevelopment) {
      console.time(`[${this.prefix}] ${label}`);
    }
  }

  timeEnd(label: string): void {
    if (isDevelopment) {
      console.timeEnd(`[${this.prefix}] ${label}`);
    }
  }
}

// Create default logger instance
export const logger = new Logger();

// Performance measurement utilities
export class PerformanceProfiler {
  private marks: Map<string, number> = new Map();
  private measures: Map<string, number> = new Map();

  mark(name: string): void {
    if (isDevelopment && 'performance' in window) {
      const timestamp = performance.now();
      this.marks.set(name, timestamp);
      performance.mark(name);
    }
  }

  measure(name: string, startMark: string, endMark?: string): number | null {
    if (!isDevelopment || !('performance' in window)) {
      return null;
    }

    try {
      if (endMark) {
        performance.measure(name, startMark, endMark);
      } else {
        performance.measure(name, startMark);
      }

      const measure = performance.getEntriesByName(name, 'measure')[0];
      const duration = measure?.duration || 0;
      this.measures.set(name, duration);
      
      logger.debug(`Performance: ${name} took ${duration.toFixed(2)}ms`);
      return duration;
    } catch (error) {
      logger.warn(`Failed to measure performance for ${name}:`, error);
      return null;
    }
  }

  getMeasure(name: string): number | undefined {
    return this.measures.get(name);
  }

  getAllMeasures(): Record<string, number> {
    return Object.fromEntries(this.measures);
  }

  clear(): void {
    if (isDevelopment && 'performance' in window) {
      performance.clearMarks();
      performance.clearMeasures();
    }
    this.marks.clear();
    this.measures.clear();
  }
}

export const profiler = new PerformanceProfiler();

// Component debugging utilities
export function debugComponent(name: string, props: Record<string, unknown>): void {
  if (isDevelopment) {
    logger.group(`Component: ${name}`);
    logger.debug('Props:', props);
    logger.groupEnd();
  }
}

export function debugRender(componentName: string, renderCount: number): void {
  if (isDevelopment) {
    logger.debug(`${componentName} rendered ${renderCount} times`);
  }
}

// Hook for debugging component renders
export function useRenderCount(componentName: string): number {
  const renderCount = React.useRef(0);
  
  React.useEffect(() => {
    renderCount.current += 1;
    debugRender(componentName, renderCount.current);
  });

  return renderCount.current;
}

// Hook for debugging prop changes
export function useWhyDidYouUpdate(name: string, props: Record<string, unknown>): void {
  const previousProps = React.useRef<Record<string, unknown> | undefined>(undefined);

  React.useEffect(() => {
    if (previousProps.current && isDevelopment) {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changedProps: Record<string, { from: unknown; to: unknown }> = {};

      allKeys.forEach(key => {
        if (previousProps.current![key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current![key],
            to: props[key],
          };
        }
      });

      if (Object.keys(changedProps).length) {
        logger.group(`[${name}] Props changed:`);
        logger.table(changedProps);
        logger.groupEnd();
      }
    }

    previousProps.current = props;
  });
}

// Memory usage monitoring
export function logMemoryUsage(): void {
  if (isDevelopment && typeof window !== 'undefined' && 'performance' in window && 'memory' in (performance as any)) {
    const memory = (performance as any).memory;
    logger.info('Memory Usage:', {
      used: `${Math.round(memory.usedJSHeapSize / 1048576)} MB`,
      total: `${Math.round(memory.totalJSHeapSize / 1048576)} MB`,
      limit: `${Math.round(memory.jsHeapSizeLimit / 1048576)} MB`,
    });
  }
}

// Bundle size analysis
export function analyzeBundleSize(): void {
  if (isDevelopment && isClient) {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    
    logger.group('Bundle Analysis');
    logger.info(`Scripts loaded: ${scripts.length}`);
    logger.info(`Stylesheets loaded: ${styles.length}`);
    
    scripts.forEach((script: Element) => {
      const src = script.getAttribute('src');
      if (src) {
        logger.debug(`Script: ${src}`);
      }
    });
    
    styles.forEach((style: Element) => {
      const href = style.getAttribute('href');
      if (href) {
        logger.debug(`Stylesheet: ${href}`);
      }
    });
    
    logger.groupEnd();
  }
}

// Error boundary for development
export function DevErrorBoundary({ children }: { children: React.ReactNode }) {
  if (!isDevelopment) {
    return React.createElement(React.Fragment, null, children);
  }

  // Note: This is a conceptual component - in practice you'd use a proper error boundary library
  // or implement a class component for error boundaries
  return React.createElement(React.Fragment, null, children);
}

// Development-only React import
import React from 'react';

// Initialize development tools
export function initializeDevTools(): void {
  if (isDevelopment && isClient) {
    logger.info('Development mode enabled');
    
    // Log environment info
    logger.info('Environment:', {
      NODE_ENV: process.env.NODE_ENV,
      userAgent: navigator.userAgent,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
    });

    // Monitor performance
    setTimeout(() => {
      logMemoryUsage();
      analyzeBundleSize();
    }, 2000);

    // Add global dev utilities
    (window as any).__DEV_UTILS__ = {
      logger,
      profiler,
      logMemoryUsage,
      analyzeBundleSize,
    };
  }
}
