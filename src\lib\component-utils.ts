// Component utilities and common patterns
import React from 'react';

import type { BaseComponentProps } from '@/types';

// Common prop types
export interface ComponentWithChildren extends BaseComponentProps {
  children: React.ReactNode;
}

export interface ComponentWithOptionalChildren extends BaseComponentProps {
  children?: React.ReactNode;
}

export interface LoadingProps {
  isLoading?: boolean;
  loadingText?: string;
  loadingComponent?: React.ComponentType;
}

export interface ErrorProps {
  error?: string | Error;
  onRetry?: () => void;
  errorComponent?: React.ComponentType<{ error: string | Error; onRetry?: () => void }>;
}

export interface AsyncComponentProps extends LoadingProps, ErrorProps {
  data?: unknown;
}

// Default props helper
export function withDefaults<T extends Record<string, unknown>>(defaultProps: Partial<T>) {
  return function <P extends T>(Component: React.ComponentType<P>) {
    const ComponentWithDefaults = (props: P) => {
      const mergedProps = { ...defaultProps, ...props } as P;
      return React.createElement(Component, mergedProps);
    };

    ComponentWithDefaults.displayName = `withDefaults(${Component.displayName || Component.name})`;
    return ComponentWithDefaults;
  };
}

// Prop validation helper
export function validateProps<T extends Record<string, unknown>>(
  props: T,
  validators: Record<keyof T, (value: unknown) => boolean | string>
): { isValid: boolean; errors: Record<keyof T, string> } {
  const errors = {} as Record<keyof T, string>;
  let isValid = true;

  Object.entries(validators).forEach(([key, validator]) => {
    const result = validator(props[key as keyof T]);
    if (typeof result === 'string') {
      errors[key as keyof T] = result;
      isValid = false;
    } else if (!result) {
      errors[key as keyof T] = `Invalid value for ${key}`;
      isValid = false;
    }
  });

  return { isValid, errors };
}

// Common validators
export const validators = {
  required: (value: unknown) => (value != null && value !== '') || 'This field is required',
  string: (value: unknown) => typeof value === 'string' || 'Must be a string',
  number: (value: unknown) => typeof value === 'number' || 'Must be a number',
  boolean: (value: unknown) => typeof value === 'boolean' || 'Must be a boolean',
  array: (value: unknown) => Array.isArray(value) || 'Must be an array',
  object: (value: unknown) => (typeof value === 'object' && value !== null) || 'Must be an object',
  email: (value: unknown) => {
    if (typeof value !== 'string') {
      return 'Must be a string';
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) || 'Must be a valid email';
  },
  url: (value: unknown) => {
    if (typeof value !== 'string') {
      return 'Must be a string';
    }
    try {
      new URL(value);
      return true;
    } catch {
      return 'Must be a valid URL';
    }
  },
  minLength: (min: number) => (value: unknown) => {
    if (typeof value !== 'string') {
      return 'Must be a string';
    }
    return value.length >= min || `Must be at least ${min} characters`;
  },
  maxLength: (max: number) => (value: unknown) => {
    if (typeof value !== 'string') {
      return 'Must be a string';
    }
    return value.length <= max || `Must be at most ${max} characters`;
  },
  oneOf:
    <T>(options: T[]) =>
    (value: unknown) => {
      return options.includes(value as T) || `Must be one of: ${options.join(', ')}`;
    },
};

// Memoization helper for expensive computations
export function useMemoizedValue<T>(factory: () => T, deps: React.DependencyList): T {
  return React.useMemo(factory, deps);
}

// Debounced value hook
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Previous value hook
export function usePrevious<T>(value: T): T | undefined {
  const ref = React.useRef<T | undefined>(undefined);
  React.useEffect(() => {
    ref.current = value;
  });
  return ref.current;
}

// Local storage hook with SSR safety
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  const [storedValue, setStoredValue] = React.useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = React.useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  return [storedValue, setValue];
}

// Media query hook
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = React.useState(false);

  React.useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const media = window.matchMedia(query);
    setMatches(media.matches);

    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  return matches;
}

// Intersection observer hook
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
): IntersectionObserverEntry | null {
  const [entry, setEntry] = React.useState<IntersectionObserverEntry | null>(null);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) {
      return;
    }

    const observer = new IntersectionObserver(([entry]) => setEntry(entry || null), options);

    observer.observe(element);
    return () => observer.disconnect();
  }, [elementRef, options]);

  return entry;
}

// Click outside hook
export function useClickOutside<T extends HTMLElement>(
  handler: () => void
): React.RefObject<T | null> {
  const ref = React.useRef<T | null>(null);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [handler]);

  return ref;
}

// Async state management hook
export function useAsyncState<T>() {
  const [state, setState] = React.useState<{
    data?: T;
    error?: string;
    isLoading: boolean;
  }>({
    isLoading: false,
  });

  const execute = React.useCallback(async (asyncFunction: () => Promise<T>) => {
    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const data = await asyncFunction();
      setState({ data, isLoading: false, error: undefined });
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setState({ error: errorMessage, isLoading: false, data: undefined });
      throw error;
    }
  }, []);

  const reset = React.useCallback(() => {
    setState({ isLoading: false, data: undefined, error: undefined });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

// Component composition helpers
export function composeComponents(
  ...components: React.ComponentType<any>[]
): React.ComponentType<any> {
  return function ComposedComponent(props: any) {
    return components.reduceRight(
      (children, Component) => React.createElement(Component, props, children),
      props.children
    );
  };
}

// Higher-order component for error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
) {
  return function WrappedComponent(props: P) {
    return React.createElement(
      React.Suspense,
      { fallback: React.createElement('div', null, 'Loading...') },
      React.createElement(Component, props)
    );
  };
}

// Performance optimization helpers
export const memo = React.memo;
export const lazy = React.lazy;
export const Suspense = React.Suspense;
