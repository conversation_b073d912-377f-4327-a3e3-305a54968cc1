'use client';

import { motion } from 'framer-motion';
import { AlertTriangle, X, RefreshCw } from 'lucide-react';

interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  variant?: 'error' | 'warning' | 'info';
  className?: string;
}

export default function ErrorMessage({
  title = 'Error',
  message,
  onRetry,
  onDismiss,
  variant = 'error',
  className = '',
}: ErrorMessageProps) {
  const variantStyles = {
    error: {
      bg: 'bg-red-50 dark:bg-red-900/20',
      border: 'border-red-200 dark:border-red-800',
      text: 'text-red-600 dark:text-red-400',
      icon: 'text-red-500',
    },
    warning: {
      bg: 'bg-yellow-50 dark:bg-yellow-900/20',
      border: 'border-yellow-200 dark:border-yellow-800',
      text: 'text-yellow-600 dark:text-yellow-400',
      icon: 'text-yellow-500',
    },
    info: {
      bg: 'bg-blue-50 dark:bg-blue-900/20',
      border: 'border-blue-200 dark:border-blue-800',
      text: 'text-blue-600 dark:text-blue-400',
      icon: 'text-blue-500',
    },
  };

  const styles = variantStyles[variant];

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`${styles.bg} ${styles.border} border rounded-xl p-4 ${className}`}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertTriangle className={`w-5 h-5 ${styles.icon}`} />
        </div>
        <div className="ml-3 flex-1">
          <h3 className={`text-sm font-semibold ${styles.text}`}>
            {title}
          </h3>
          <p className={`mt-1 text-sm ${styles.text}`}>
            {message}
          </p>
          {onRetry && (
            <div className="mt-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onRetry}
                className={`inline-flex items-center space-x-2 px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${styles.text} hover:bg-white/50 dark:hover:bg-black/20`}
              >
                <RefreshCw className="w-3 h-3" />
                <span>Try Again</span>
              </motion.button>
            </div>
          )}
        </div>
        {onDismiss && (
          <div className="flex-shrink-0 ml-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onDismiss}
              className={`${styles.text} hover:opacity-70 transition-opacity`}
            >
              <X className="w-4 h-4" />
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  );
}

export function ErrorBoundaryFallback({ 
  error, 
  resetError, 
}: { 
  error: Error; 
  resetError: () => void; 
}) {
  return (
    <div className="min-h-screen bg-background-light dark:bg-background-dark flex items-center justify-center p-6">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-md w-full bg-surface-light dark:bg-surface-dark rounded-2xl border border-border-light dark:border-border-dark p-8 text-center"
      >
        <div className="w-16 h-16 mx-auto mb-6 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
          <AlertTriangle className="w-8 h-8 text-red-500" />
        </div>
        <h2 className="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-3">
          Something went wrong
        </h2>
        <p className="text-text-secondary-light dark:text-text-secondary-dark mb-6">
          {error.message || 'An unexpected error occurred. Please try again.'}
        </p>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={resetError}
          className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg"
        >
          Try Again
        </motion.button>
      </motion.div>
    </div>
  );
}
