'use client';

import { motion } from 'framer-motion';
import React from 'react';

import { cn } from '@/lib/utils';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  border?: boolean;
  gradient?: boolean;
  onClick?: () => void;
}

const paddingVariants = {
  none: '',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
};

const roundedVariants = {
  none: '',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
};

const shadowVariants = {
  none: '',
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
};

export default function Card({
  children,
  className,
  hover = false,
  padding = 'md',
  rounded = 'lg',
  shadow = 'md',
  border = true,
  gradient = false,
  onClick,
}: CardProps) {
  const isClickable = !!onClick;

  return (
    <motion.div
      whileHover={hover ? { y: -4, scale: 1.02 } : {}}
      whileTap={isClickable ? { scale: 0.98 } : {}}
      onClick={onClick}
      className={cn(
        'bg-background-light dark:bg-gray-800 transition-all duration-200',
        paddingVariants[padding],
        roundedVariants[rounded],
        shadowVariants[shadow],
        border && 'border border-border-light dark:border-border-dark',
        hover && 'hover:shadow-xl dark:hover:shadow-2xl hover:border-primary-500/25 glow-hover',
        gradient && 'bg-gradient-to-br from-background-light to-surface-light dark:from-gray-800 dark:to-gray-900',
        isClickable && 'cursor-pointer',
        className,
      )}
    >
      {children}
    </motion.div>
  );
}

// Sub-components for better composition
Card.Header = function CardHeader({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn('mb-4', className)}>
      {children}
    </div>
  );
};

Card.Title = function CardTitle({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <h3 className={cn('text-xl font-semibold text-text-primary-light dark:text-text-primary-dark', className)}>
      {children}
    </h3>
  );
};

Card.Description = function CardDescription({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <p className={cn('text-text-secondary-light dark:text-text-secondary-dark', className)}>
      {children}
    </p>
  );
};

Card.Content = function CardContent({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn('', className)}>
      {children}
    </div>
  );
};

Card.Footer = function CardFooter({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn('mt-4 pt-4 border-t border-border-light dark:border-border-dark', className)}>
      {children}
    </div>
  );
};
