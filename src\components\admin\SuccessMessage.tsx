'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface SuccessMessageProps {
  title?: string;
  message: string;
  onDismiss?: () => void;
  autoHide?: boolean;
  duration?: number;
  className?: string;
}

export default function SuccessMessage({
  title = 'Success',
  message,
  onDismiss,
  autoHide = true,
  duration = 5000,
  className = '',
}: SuccessMessageProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => onDismiss?.(), 300);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [autoHide, duration, onDismiss]);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => onDismiss?.(), 300);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -10, scale: 0.95 }}
          className={`bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 ${className}`}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: 'spring', stiffness: 300 }}
              >
                <CheckCircle className="w-5 h-5 text-green-500" />
              </motion.div>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-semibold text-green-600 dark:text-green-400">
                {title}
              </h3>
              <p className="mt-1 text-sm text-green-600 dark:text-green-400">
                {message}
              </p>
            </div>
            {onDismiss && (
              <div className="flex-shrink-0 ml-4">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleDismiss}
                  className="text-green-600 dark:text-green-400 hover:opacity-70 transition-opacity"
                >
                  <X className="w-4 h-4" />
                </motion.button>
              </div>
            )}
          </div>
          {autoHide && (
            <motion.div
              initial={{ width: '100%' }}
              animate={{ width: '0%' }}
              transition={{ duration: duration / 1000, ease: 'linear' }}
              className="mt-3 h-1 bg-green-500 rounded-full"
            />
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export function SuccessToast({
  message,
  isVisible,
  onDismiss,
}: {
  message: string;
  isVisible: boolean;
  onDismiss: () => void;
}) {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(onDismiss, 3000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onDismiss]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50, x: '-50%' }}
          animate={{ opacity: 1, y: 0, x: '-50%' }}
          exit={{ opacity: 0, y: -50, x: '-50%' }}
          className="fixed top-4 left-1/2 z-toast bg-green-500 text-white px-6 py-3 rounded-xl shadow-lg flex items-center space-x-2"
        >
          <CheckCircle className="w-5 h-5" />
          <span className="font-medium">{message}</span>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Note: This component is now deprecated. Use the new Toast component from @/components/ui/Toast instead.
