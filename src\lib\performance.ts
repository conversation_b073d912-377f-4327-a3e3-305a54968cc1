// Performance optimization utilities
import { PerformanceMetrics } from '@/types';

// Resource preloading
export function preloadRoute(href: string, priority: 'high' | 'low' = 'low') {
  if (typeof window === 'undefined') return;

  const link = document.createElement('link');
  link.rel = priority === 'high' ? 'preload' : 'prefetch';
  link.href = href;
  link.as = 'document';

  // Avoid duplicate preloads
  const existing = document.querySelector(`link[href="${href}"]`);
  if (!existing) {
    document.head.appendChild(link);
  }
}

export function preloadImage(src: string, priority: 'high' | 'low' = 'low'): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.loading = priority === 'high' ? 'eager' : 'lazy';
    img.src = src;
  });
}

export function preloadCriticalResources(resources: string[]) {
  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';

    if (resource.endsWith('.css')) {
      link.as = 'style';
    } else if (resource.endsWith('.js')) {
      link.as = 'script';
    } else if (resource.match(/\.(woff2?|ttf|otf)$/)) {
      link.as = 'font';
      link.crossOrigin = 'anonymous';
    } else if (resource.match(/\.(jpg|jpeg|png|webp|avif)$/)) {
      link.as = 'image';
    }

    link.href = resource;
    document.head.appendChild(link);
  });
}

// Enhanced lazy loading with intersection observer
export function lazyLoadImages(options: IntersectionObserverInit = {}) {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return;
  }

  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px 0px',
    threshold: 0.01,
    ...options,
  };

  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        const src = img.dataset.src;
        const srcset = img.dataset.srcset;

        if (src) {
          img.src = src;
          img.removeAttribute('data-src');
        }

        if (srcset) {
          img.srcset = srcset;
          img.removeAttribute('data-srcset');
        }

        img.classList.remove('lazy');
        img.classList.add('loaded');
        observer.unobserve(img);
      }
    });
  }, defaultOptions);

  // Observe all lazy images
  document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
  });

  return imageObserver;
}

export function optimizeBundle() {
  // Dynamic imports for code splitting
  const loadComponent = (componentName: string) => {
    switch (componentName) {
      case 'Button':
        return import('@/components/ui/Button');
      case 'Card':
        return import('@/components/ui/Card');
      default:
        return Promise.reject(new Error(`Component ${componentName} not found`));
    }
  };

  return loadComponent;
}

// Core Web Vitals measurement
export function measureCoreWebVitals(): Promise<PerformanceMetrics> {
  return new Promise((resolve) => {
    const metrics: Partial<PerformanceMetrics> = {};
    let metricsCollected = 0;
    const totalMetrics = 5;

    const checkComplete = () => {
      metricsCollected++;
      if (metricsCollected === totalMetrics) {
        resolve(metrics as PerformanceMetrics);
      }
    };

    // First Contentful Paint (FCP)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
        checkComplete();
      }
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint (LCP)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      metrics.lcp = lastEntry.startTime;
      checkComplete();
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const firstEntry = entries[0];
      metrics.fid = firstEntry.processingStart - firstEntry.startTime;
      checkComplete();
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      metrics.cls = clsValue;
      checkComplete();
    }).observe({ entryTypes: ['layout-shift'] });

    // Time to First Byte (TTFB)
    const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigationEntry) {
      metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      checkComplete();
    }

    // Fallback timeout
    setTimeout(() => {
      resolve(metrics as PerformanceMetrics);
    }, 10000);
  });
}

export function measurePerformance() {
  if (typeof window === 'undefined' || !('performance' in window)) {
    return;
  }

  // Enhanced performance measurement
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      console.log(`${entry.name}: ${entry.startTime}ms`);

      // Send to analytics in production
      if (process.env.NODE_ENV === 'production') {
        // sendToAnalytics(entry);
      }
    });
  });

  observer.observe({
    entryTypes: ['measure', 'navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift']
  });

  // Custom performance marks
  performance.mark('app-start');

  window.addEventListener('load', () => {
    performance.mark('app-loaded');
    performance.measure('app-load-time', 'app-start', 'app-loaded');

    // Measure Core Web Vitals
    measureCoreWebVitals().then(metrics => {
      console.log('Core Web Vitals:', metrics);

      // Send to analytics
      if (process.env.NODE_ENV === 'production') {
        // sendCoreWebVitalsToAnalytics(metrics);
      }
    });
  });
}

export function optimizeScrolling() {
  let ticking = false;

  function updateScrollPosition() {
    // Update scroll-dependent elements
    const scrollY = window.pageYOffset;
    document.documentElement.style.setProperty('--scroll-y', `${scrollY}px`);
    ticking = false;
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateScrollPosition);
      ticking = true;
    }
  }

  window.addEventListener('scroll', requestTick, { passive: true });
}

export function enableServiceWorker() {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
}

export function optimizeImages() {
  // Add loading="lazy" to images below the fold
  const images = document.querySelectorAll('img');
  images.forEach((img, index) => {
    if (index > 2) { // Skip first 3 images (above the fold)
      img.loading = 'lazy';
    }
  });
}

export function prefetchCriticalResources() {
  const criticalResources = [
    '/fonts/inter.woff2',
    '/fonts/jetbrains-mono.woff2',
    '/fonts/space-grotesk.woff2',
  ];

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource;
    link.as = 'font';
    link.type = 'font/woff2';
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
}

export function initializePerformanceOptimizations() {
  if (typeof window !== 'undefined') {
    measurePerformance();
    optimizeScrolling();
    lazyLoadImages();
    optimizeImages();
    
    // Prefetch critical resources
    document.addEventListener('DOMContentLoaded', () => {
      prefetchCriticalResources();
    });

    // Preload routes on hover
    document.addEventListener('mouseover', (e) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href^="/"]') as HTMLAnchorElement;
      if (link && !link.dataset.prefetched) {
        preloadRoute(link.href);
        link.dataset.prefetched = 'true';
      }
    });
  }
}

// Web Vitals measurement
export function measureWebVitals() {
  if (typeof window !== 'undefined') {
    // Largest Contentful Paint
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry) => {
        console.log('FID:', entry.processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      console.log('CLS:', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }
}
