import { NextResponse } from 'next/server';

import { getPortfolioData } from '@/lib/storage';

export async function GET() {
  try {
    const data = await getPortfolioData();
    return NextResponse.json({ projects: data.projects || [] });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 },
    );
  }
}
