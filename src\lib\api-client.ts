import type { ApiResponse } from '@/types';

// API Error classes
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: Record<string, unknown>,
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class NetworkError extends Error {
  constructor(message: string = 'Network request failed') {
    super(message);
    this.name = 'NetworkError';
  }
}

export class TimeoutError extends Error {
  constructor(message: string = 'Request timeout') {
    super(message);
    this.name = 'TimeoutError';
  }
}

// Retry configuration
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: Error) => boolean;
}

const defaultRetryConfig: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error) => {
    if (error instanceof ApiError) {
      // Retry on server errors but not client errors
      return error.status >= 500;
    }
    if (error instanceof NetworkError || error instanceof TimeoutError) {
      return true;
    }
    return false;
  },
};

// Request configuration
interface RequestConfig extends RequestInit {
  timeout?: number;
  retry?: Partial<RetryConfig>;
  skipErrorHandling?: boolean;
}

// Enhanced fetch with error handling and retries
export async function apiRequest<T = unknown>(
  url: string,
  config: RequestConfig = {},
): Promise<ApiResponse<T>> {
  const {
    timeout = 10000,
    retry = {},
    skipErrorHandling = false,
    ...fetchConfig
  } = config;

  const retryConfig = { ...defaultRetryConfig, ...retry };
  let lastError: Error = new Error('Unknown error');

  for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        ...fetchConfig,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...fetchConfig.headers,
        },
      });

      clearTimeout(timeoutId);

      // Handle HTTP errors
      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorDetails: Record<string, unknown> = {};

        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
          errorDetails = errorData.details || {};
        } catch {
          // If response is not JSON, use status text
        }

        throw new ApiError(errorMessage, response.status, response.statusText, errorDetails);
      }

      // Parse successful response
      const data = await response.json();
      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };

    } catch (error) {
      // Handle different error types
      if (error instanceof DOMException && error.name === 'AbortError') {
        lastError = new TimeoutError(`Request timeout after ${timeout}ms`);
      } else if (error instanceof TypeError && error.message.includes('fetch')) {
        lastError = new NetworkError('Network request failed');
      } else if (error instanceof ApiError) {
        lastError = error;
      } else {
        lastError = new Error(error instanceof Error ? error.message : 'Unknown error');
      }

      // Check if we should retry
      const shouldRetry = attempt < retryConfig.maxAttempts && 
                         retryConfig.retryCondition?.(lastError);

      if (!shouldRetry) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        retryConfig.baseDelay * Math.pow(retryConfig.backoffFactor, attempt - 1),
        retryConfig.maxDelay,
      );

      // Add jitter to prevent thundering herd
      const jitteredDelay = delay + Math.random() * 1000;

      console.warn(`API request failed (attempt ${attempt}/${retryConfig.maxAttempts}), retrying in ${Math.round(jitteredDelay)}ms...`, lastError);

      await new Promise(resolve => setTimeout(resolve, jitteredDelay));
    }
  }

  // All retries exhausted, handle the error
  if (!skipErrorHandling) {
    handleApiError(lastError);
  }

  return {
    success: false,
    error: lastError.message,
  };
}

// Centralized error handling
function handleApiError(error: Error) {
  console.error('API Error:', error);

  // You can add global error handling here
  // For example, show toast notifications, redirect to login, etc.
  
  if (error instanceof ApiError) {
    switch (error.status) {
      case 401:
        // Handle unauthorized - maybe redirect to login
        console.warn('Unauthorized access - consider redirecting to login');
        break;
      case 403:
        // Handle forbidden
        console.warn('Access forbidden');
        break;
      case 404:
        // Handle not found
        console.warn('Resource not found');
        break;
      case 429:
        // Handle rate limiting
        console.warn('Rate limit exceeded');
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        // Handle server errors
        console.error('Server error occurred');
        break;
    }
  }
}

// Convenience methods
export const api = {
  get: <T = unknown>(url: string, config?: Omit<RequestConfig, 'method' | 'body'>) =>
    apiRequest<T>(url, { ...config, method: 'GET' }),

  post: <T = unknown>(url: string, data?: unknown, config?: Omit<RequestConfig, 'method'>) =>
    apiRequest<T>(url, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : null,
    }),

  put: <T = unknown>(url: string, data?: unknown, config?: Omit<RequestConfig, 'method'>) =>
    apiRequest<T>(url, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : null,
    }),

  patch: <T = unknown>(url: string, data?: unknown, config?: Omit<RequestConfig, 'method'>) =>
    apiRequest<T>(url, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : null,
    }),

  delete: <T = unknown>(url: string, config?: Omit<RequestConfig, 'method' | 'body'>) =>
    apiRequest<T>(url, { ...config, method: 'DELETE' }),
};

// Hook for API requests with React state management
export function useApiRequest<T = unknown>() {
  const [state, setState] = React.useState<{
    data?: T;
    error?: string;
    isLoading: boolean;
  }>({
    isLoading: false,
  });

  const execute = React.useCallback(async (
    url: string,
    config?: RequestConfig,
  ) => {
    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await apiRequest<T>(url, config);

      if (response.success) {
        setState({ data: response.data, isLoading: false, error: undefined });
        return response.data;
      } else {
        setState({ error: response.error, isLoading: false, data: undefined });
        return null;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState({ error: errorMessage, isLoading: false, data: undefined });
      return null;
    }
  }, []);

  const reset = React.useCallback(() => {
    setState({ isLoading: false, data: undefined, error: undefined });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

// React import for the hook
import React from 'react';
