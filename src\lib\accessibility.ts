// Accessibility utilities and helpers

// Screen reader announcements
export function announceToScreenReader(
  message: string,
  priority: 'polite' | 'assertive' = 'polite',
  atomic: boolean = true
) {
  if (typeof window === 'undefined' || !message.trim()) return;

  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', atomic.toString());
  announcement.setAttribute('class', 'sr-only');
  announcement.textContent = message.trim();

  // Add unique ID for tracking
  const id = `announcement-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  announcement.id = id;

  document.body.appendChild(announcement);

  // Clean up after announcement
  setTimeout(() => {
    const element = document.getElementById(id);
    if (element && element.parentNode) {
      element.parentNode.removeChild(element);
    }
  }, 1000);
}

// Enhanced screen reader utilities
export function createLiveRegion(
  id: string,
  priority: 'polite' | 'assertive' = 'polite'
): HTMLElement {
  let region = document.getElementById(id);

  if (!region) {
    region = document.createElement('div');
    region.id = id;
    region.setAttribute('aria-live', priority);
    region.setAttribute('aria-atomic', 'true');
    region.className = 'sr-only';
    document.body.appendChild(region);
  }

  return region;
}

export function updateLiveRegion(id: string, message: string) {
  const region = document.getElementById(id);
  if (region) {
    region.textContent = message;
  }
}

export function trapFocus(element: HTMLElement) {
  const focusableElements = element.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  const firstFocusableElement = focusableElements[0] as HTMLElement;
  const lastFocusableElement = focusableElements[focusableElements.length - 1] as HTMLElement;

  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key !== 'Tab') return;

    if (e.shiftKey) {
      if (document.activeElement === firstFocusableElement) {
        lastFocusableElement.focus();
        e.preventDefault();
      }
    } else {
      if (document.activeElement === lastFocusableElement) {
        firstFocusableElement.focus();
        e.preventDefault();
      }
    }
  };

  element.addEventListener('keydown', handleTabKey);
  
  return () => {
    element.removeEventListener('keydown', handleTabKey);
  };
}

export function getContrastRatio(color1: string, color2: string): number {
  // Simplified contrast ratio calculation
  // In a real implementation, you'd want a more robust color parsing library
  const getLuminance = (color: string) => {
    // This is a simplified version - you'd want proper color parsing
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;
    
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

export function isAccessibleContrast(foreground: string, background: string, level: 'AA' | 'AAA' = 'AA'): boolean {
  const ratio = getContrastRatio(foreground, background);
  return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
}

export function addSkipLink() {
  const skipLink = document.createElement('a');
  skipLink.href = '#main-content';
  skipLink.textContent = 'Skip to main content';
  skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary-500 focus:text-white focus:rounded-lg';
  
  document.body.insertBefore(skipLink, document.body.firstChild);
}

export function handleReducedMotion() {
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
  
  if (prefersReducedMotion.matches) {
    document.documentElement.style.setProperty('--animation-duration', '0.01ms');
    document.documentElement.style.setProperty('--transition-duration', '0.01ms');
  }
  
  prefersReducedMotion.addEventListener('change', (e) => {
    if (e.matches) {
      document.documentElement.style.setProperty('--animation-duration', '0.01ms');
      document.documentElement.style.setProperty('--transition-duration', '0.01ms');
    } else {
      document.documentElement.style.removeProperty('--animation-duration');
      document.documentElement.style.removeProperty('--transition-duration');
    }
  });
}

export function addAriaLabels() {
  // Add aria-labels to elements that need them
  const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
  buttons.forEach((button) => {
    const text = button.textContent?.trim();
    if (text) {
      button.setAttribute('aria-label', text);
    }
  });
}

export function ensureHeadingHierarchy() {
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let currentLevel = 0;
  
  headings.forEach((heading) => {
    const level = parseInt(heading.tagName.charAt(1));
    
    if (level > currentLevel + 1) {
      console.warn(`Heading hierarchy issue: ${heading.tagName} follows h${currentLevel}`, heading);
    }
    
    currentLevel = level;
  });
}

export function addLandmarkRoles() {
  // Ensure proper landmark roles are present
  const nav = document.querySelector('nav');
  if (nav && !nav.getAttribute('role')) {
    nav.setAttribute('role', 'navigation');
  }
  
  const main = document.querySelector('main');
  if (main && !main.getAttribute('role')) {
    main.setAttribute('role', 'main');
  }
  
  const footer = document.querySelector('footer');
  if (footer && !footer.getAttribute('role')) {
    footer.setAttribute('role', 'contentinfo');
  }
}

// Keyboard navigation helpers
export function createRovingTabIndex(container: HTMLElement, selector: string = '[role="menuitem"], button, a[href]') {
  const items = Array.from(container.querySelectorAll(selector)) as HTMLElement[];
  let currentIndex = 0;

  // Set initial tab indices
  items.forEach((item, index) => {
    item.tabIndex = index === 0 ? 0 : -1;
  });

  const handleKeyDown = (event: KeyboardEvent) => {
    const { key } = event;

    if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(key)) {
      return;
    }

    event.preventDefault();

    // Update current index based on key
    switch (key) {
      case 'ArrowUp':
      case 'ArrowLeft':
        currentIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        break;
      case 'ArrowDown':
      case 'ArrowRight':
        currentIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'Home':
        currentIndex = 0;
        break;
      case 'End':
        currentIndex = items.length - 1;
        break;
    }

    // Update tab indices and focus
    items.forEach((item, index) => {
      item.tabIndex = index === currentIndex ? 0 : -1;
    });

    items[currentIndex]?.focus();
  };

  container.addEventListener('keydown', handleKeyDown);

  return () => {
    container.removeEventListener('keydown', handleKeyDown);
  };
}

// Enhanced focus management
export function createFocusManager() {
  let focusHistory: HTMLElement[] = [];

  return {
    saveFocus: () => {
      const activeElement = document.activeElement as HTMLElement;
      if (activeElement && activeElement !== document.body) {
        focusHistory.push(activeElement);
      }
    },

    restoreFocus: () => {
      const lastFocused = focusHistory.pop();
      if (lastFocused && document.contains(lastFocused)) {
        lastFocused.focus();
      }
    },

    clearHistory: () => {
      focusHistory = [];
    }
  };
}

// ARIA helpers
export function setAriaExpanded(element: HTMLElement, expanded: boolean) {
  element.setAttribute('aria-expanded', expanded.toString());
}

export function setAriaSelected(element: HTMLElement, selected: boolean) {
  element.setAttribute('aria-selected', selected.toString());
}

export function setAriaChecked(element: HTMLElement, checked: boolean | 'mixed') {
  element.setAttribute('aria-checked', checked.toString());
}

export function associateLabels(input: HTMLElement, labelIds: string[]) {
  input.setAttribute('aria-labelledby', labelIds.join(' '));
}

export function associateDescriptions(input: HTMLElement, descriptionIds: string[]) {
  input.setAttribute('aria-describedby', descriptionIds.join(' '));
}

// Enhanced accessibility initialization
export function initializeAccessibility() {
  if (typeof window === 'undefined') return;

  // Create global live regions
  createLiveRegion('global-announcements', 'polite');
  createLiveRegion('global-alerts', 'assertive');

  // Initialize core accessibility features
  addSkipLink();
  handleReducedMotion();

  // Run after DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDOMAccessibility);
  } else {
    initializeDOMAccessibility();
  }

  // Monitor for dynamic content changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Re-run accessibility checks for new content
        setTimeout(() => {
          addAriaLabels();
          addLandmarkRoles();
        }, 100);
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

function initializeDOMAccessibility() {
  addAriaLabels();
  ensureHeadingHierarchy();
  addLandmarkRoles();

  // Add focus indicators for keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      document.body.classList.add('keyboard-navigation');
    }
  });

  document.addEventListener('mousedown', () => {
    document.body.classList.remove('keyboard-navigation');
  });
}
