import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

import { getAuthenticatedUser } from '@/lib/auth-server';
import { getPortfolioData, savePortfolioData } from '@/lib/storage';

export async function GET() {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await getPortfolioData();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch data' },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const updates = await request.json();
    const result = await savePortfolioData(updates);

    if (result.success) {
      return NextResponse.json({ success: true, data: updates });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error updating portfolio data:', error);
    return NextResponse.json(
      { error: 'Failed to update data' },
      { status: 500 },
    );
  }
}
