'use client';

import { motion } from 'framer-motion';
import { Save, Globe, Search, User, Shield, Bell, Palette } from 'lucide-react';
import { useState, useEffect } from 'react';

import AdminProtectedLayout from '@/components/admin/AdminProtectedLayout';

interface SettingsData {
  seo: {
    title: string;
    description: string;
    keywords: string[];
    author: string;
    siteUrl: string;
    image: string;
  };
  site: {
    name: string;
    tagline: string;
    logo: string;
    favicon: string;
    analyticsId: string;
  };
  admin: {
    email: string;
    name: string;
    notifications: {
      emailOnContact: boolean;
      emailOnComment: boolean;
      weeklyReports: boolean;
    };
  };
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SettingsData>({
    seo: {
      title: '',
      description: '',
      keywords: [],
      author: '',
      siteUrl: '',
      image: '',
    },
    site: {
      name: '',
      tagline: '',
      logo: '',
      favicon: '',
      analyticsId: '',
    },
    admin: {
      email: '',
      name: '',
      notifications: {
        emailOnContact: true,
        emailOnComment: true,
        weeklyReports: false,
      },
    },
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('seo');
  const [newKeyword, setNewKeyword] = useState('');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/portfolio');
      if (response.ok) {
        const data = await response.json();
        setSettings({
          seo: data.seo || settings.seo,
          site: {
            name: data.personal?.name || '',
            tagline: data.personal?.tagline || '',
            logo: '',
            favicon: '',
            analyticsId: '',
          },
          admin: {
            email: data.personal?.email || '',
            name: data.personal?.name || '',
            notifications: settings.admin.notifications,
          },
        });
      }
    } catch (error) {
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Get current portfolio data
      const portfolioResponse = await fetch('/api/admin/portfolio');
      const portfolioData = await portfolioResponse.json();

      // Update with new settings
      const updatedData = {
        ...portfolioData,
        seo: settings.seo,
        personal: {
          ...portfolioData.personal,
          name: settings.site.name,
          tagline: settings.site.tagline,
          email: settings.admin.email,
        },
      };

      const response = await fetch('/api/admin/portfolio', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      });

      if (response.ok) {
        setSuccess('Settings saved successfully!');
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError('Failed to save settings');
      }
    } catch (error) {
      setError('Network error');
    } finally {
      setSaving(false);
    }
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !settings.seo.keywords.includes(newKeyword.trim())) {
      setSettings({
        ...settings,
        seo: {
          ...settings.seo,
          keywords: [...settings.seo.keywords, newKeyword.trim()],
        },
      });
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setSettings({
      ...settings,
      seo: {
        ...settings.seo,
        keywords: settings.seo.keywords.filter(k => k !== keyword),
      },
    });
  };

  const tabs = [
    { id: 'seo', name: 'SEO Settings', icon: Search },
    { id: 'site', name: 'Site Settings', icon: Globe },
    { id: 'admin', name: 'Admin Settings', icon: User },
  ];

  if (loading) {
    return (
      <AdminProtectedLayout
        title="Settings"
        subtitle="Loading..."
      >
        <div className="p-6">
          <div className="animate-pulse">Loading settings...</div>
        </div>
      </AdminProtectedLayout>
    );
  }

  return (
    <AdminProtectedLayout
      title="Settings"
      subtitle="Configure your site settings and preferences"
    >
      <div className="bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark px-6 py-4">
        <div className="flex items-center justify-end">
          <button
            onClick={handleSave}
            disabled={saving}
            className="inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors glow-hover"
          >
            {saving ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </div>

      <div className="p-6">
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <p className="text-green-600 dark:text-green-400">{success}</p>
          </div>
        )}

        <div className="max-w-6xl mx-auto">
          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-fit">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <tab.icon className="w-4 h-4 mr-2" />
                  {tab.name}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'seo' && (
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center mb-6">
                  <Search className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" />
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">SEO Settings</h2>
                </div>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Site Title
                      </label>
                      <input
                        type="text"
                        value={settings.seo.title}
                        onChange={(e) => setSettings({
                          ...settings,
                          seo: { ...settings.seo, title: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Your Site Title"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Author
                      </label>
                      <input
                        type="text"
                        value={settings.seo.author}
                        onChange={(e) => setSettings({
                          ...settings,
                          seo: { ...settings.seo, author: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Your Name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Meta Description
                    </label>
                    <textarea
                      rows={3}
                      value={settings.seo.description}
                      onChange={(e) => setSettings({
                        ...settings,
                        seo: { ...settings.seo, description: e.target.value },
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Brief description of your site for search engines"
                    />
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Site URL
                      </label>
                      <input
                        type="url"
                        value={settings.seo.siteUrl}
                        onChange={(e) => setSettings({
                          ...settings,
                          seo: { ...settings.seo, siteUrl: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="https://yoursite.com"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        OG Image URL
                      </label>
                      <input
                        type="url"
                        value={settings.seo.image}
                        onChange={(e) => setSettings({
                          ...settings,
                          seo: { ...settings.seo, image: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="https://yoursite.com/og-image.jpg"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      SEO Keywords
                    </label>
                    <div className="flex space-x-2 mb-2">
                      <input
                        type="text"
                        value={newKeyword}
                        onChange={(e) => setNewKeyword(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Add keyword"
                      />
                      <button
                        type="button"
                        onClick={addKeyword}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Add
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {settings.seo.keywords.map((keyword) => (
                        <span
                          key={keyword}
                          className="inline-flex items-center px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 text-sm rounded-full"
                        >
                          {keyword}
                          <button
                            type="button"
                            onClick={() => removeKeyword(keyword)}
                            className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'site' && (
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center mb-6">
                  <Globe className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Site Settings</h2>
                </div>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Site Name
                      </label>
                      <input
                        type="text"
                        value={settings.site.name}
                        onChange={(e) => setSettings({
                          ...settings,
                          site: { ...settings.site, name: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Your Site Name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tagline
                      </label>
                      <input
                        type="text"
                        value={settings.site.tagline}
                        onChange={(e) => setSettings({
                          ...settings,
                          site: { ...settings.site, tagline: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Your site tagline"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Google Analytics ID
                    </label>
                    <input
                      type="text"
                      value={settings.site.analyticsId}
                      onChange={(e) => setSettings({
                        ...settings,
                        site: { ...settings.site, analyticsId: e.target.value },
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="G-XXXXXXXXXX"
                    />
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Enter your Google Analytics measurement ID to track site analytics
                    </p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'admin' && (
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center mb-6">
                  <User className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3" />
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Admin Settings</h2>
                </div>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Admin Name
                      </label>
                      <input
                        type="text"
                        value={settings.admin.name}
                        onChange={(e) => setSettings({
                          ...settings,
                          admin: { ...settings.admin, name: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Your Name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Admin Email
                      </label>
                      <input
                        type="email"
                        value={settings.admin.email}
                        onChange={(e) => setSettings({
                          ...settings,
                          admin: { ...settings.admin, email: e.target.value },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
                      <Bell className="w-4 h-4 mr-2" />
                      Notification Preferences
                    </h3>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.admin.notifications.emailOnContact}
                          onChange={(e) => setSettings({
                            ...settings,
                            admin: {
                              ...settings.admin,
                              notifications: {
                                ...settings.admin.notifications,
                                emailOnContact: e.target.checked,
                              },
                            },
                          })}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Email notifications for new contact messages
                        </span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.admin.notifications.emailOnComment}
                          onChange={(e) => setSettings({
                            ...settings,
                            admin: {
                              ...settings.admin,
                              notifications: {
                                ...settings.admin.notifications,
                                emailOnComment: e.target.checked,
                              },
                            },
                          })}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Email notifications for new comments
                        </span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.admin.notifications.weeklyReports}
                          onChange={(e) => setSettings({
                            ...settings,
                            admin: {
                              ...settings.admin,
                              notifications: {
                                ...settings.admin.notifications,
                                weeklyReports: e.target.checked,
                              },
                            },
                          })}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Weekly analytics reports
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </AdminProtectedLayout>
  );
}
