'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { Sun, Moon, Star, Sparkles } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { useTheme } from '@/lib/theme-context';
import { cn } from '@/lib/utils';

export default function AnimatedThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <motion.button
      onClick={toggleTheme}
      className={cn(
        'fixed z-[var(--z-fixed)] p-4',
        'focus:outline-none',
        'group cursor-pointer',
        // Responsive positioning
        'top-4 right-4 sm:top-6 sm:right-6',
        // Ensure it doesn't interfere with mobile sidebar toggle
        'md:top-6 md:right-6'
      )}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <div className="relative w-12 h-12 flex items-center justify-center overflow-visible">
        <AnimatePresence mode="wait">
          {isDark ? (
            // Dark Mode - Moon with twinkling stars
            <motion.div
              key="dark"
              initial={{ opacity: 0, rotate: -90, scale: 0.5 }}
              animate={{ opacity: 1, rotate: 0, scale: 1 }}
              exit={{ opacity: 0, rotate: 90, scale: 0.5 }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              className="relative w-12 h-12 flex items-center justify-center"
            >
              {/* Moon */}
              <motion.div
                animate={{
                  y: [0, -2, 0],
                  rotate: [0, 5, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
                className="relative z-10"
              >
                <Moon
                  size={28}
                  className="text-blue-400"
                  style={{
                    filter:
                      'drop-shadow(0 0 8px rgba(96, 165, 250, 0.6)) drop-shadow(0 0 16px rgba(96, 165, 250, 0.3))',
                  }}
                />
              </motion.div>

              {/* Twinkling Stars */}
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute"
                  style={{
                    top: i === 0 ? '-8px' : i === 1 ? '24px' : i === 2 ? '8px' : '16px',
                    left: i === 0 ? '24px' : i === 1 ? '8px' : i === 2 ? '-8px' : '32px',
                  }}
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.5,
                    ease: 'easeInOut',
                  }}
                >
                  <Star size={8} className="text-blue-300" />
                </motion.div>
              ))}

              {/* Sparkles */}
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={`sparkle-${i}`}
                  className="absolute"
                  style={{
                    top: i === 0 ? '4px' : i === 1 ? '20px' : '12px',
                    left: i === 0 ? '28px' : i === 1 ? '4px' : '36px',
                  }}
                  animate={{
                    opacity: [0, 0.8, 0],
                    rotate: [0, 180, 360],
                    scale: [0, 1, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: i * 0.8,
                    ease: 'easeInOut',
                  }}
                >
                  <Sparkles size={6} className="text-purple-300" />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            // Light Mode - Realistic Sun with animated light effects
            <motion.div
              key="light"
              initial={{ opacity: 0, rotate: 90, scale: 0.5 }}
              animate={{ opacity: 1, rotate: 0, scale: 1 }}
              exit={{ opacity: 0, rotate: -90, scale: 0.5 }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              className="relative w-12 h-12 flex items-center justify-center"
            >
              {/* Outer Glow Layers */}
              <motion.div
                className="absolute inset-0 rounded-full"
                style={{
                  background:
                    'radial-gradient(circle, rgba(251, 191, 36, 0.3) 0%, rgba(251, 191, 36, 0.1) 40%, transparent 70%)',
                  width: '48px',
                  height: '48px',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                }}
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.4, 0.8, 0.4],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              />

              {/* Inner Glow */}
              <motion.div
                className="absolute inset-0 rounded-full"
                style={{
                  background:
                    'radial-gradient(circle, rgba(251, 191, 36, 0.6) 0%, rgba(251, 191, 36, 0.2) 60%, transparent 80%)',
                  width: '32px',
                  height: '32px',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                }}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.6, 1, 0.6],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              />

              {/* Radial Light Rays - Primary Set */}
              {[...Array(12)].map((_, i) => (
                <motion.div
                  key={`ray-${i}`}
                  className="absolute"
                  style={{
                    width: '2px',
                    height: '16px',
                    background:
                      'linear-gradient(to top, rgba(251, 191, 36, 0.8), rgba(255, 159, 28, 0.4), transparent)',
                    borderRadius: '1px',
                    top: '50%',
                    left: '50%',
                    transformOrigin: '50% 100%',
                    transform: `translate(-50%, -100%) rotate(${i * 30}deg) translateY(-8px)`,
                  }}
                  animate={{
                    opacity: [0.3, 1, 0.3],
                    scaleY: [0.6, 1.4, 0.6],
                    rotate: [i * 30, i * 30 + 360],
                  }}
                  transition={{
                    opacity: { duration: 2.5, repeat: Infinity, delay: i * 0.1, ease: 'easeInOut' },
                    scaleY: { duration: 2.5, repeat: Infinity, delay: i * 0.1, ease: 'easeInOut' },
                    rotate: { duration: 20, repeat: Infinity, ease: 'linear' },
                  }}
                />
              ))}

              {/* Secondary Light Rays - Shorter */}
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={`short-ray-${i}`}
                  className="absolute"
                  style={{
                    width: '1px',
                    height: '10px',
                    background:
                      'linear-gradient(to top, rgba(255, 159, 28, 0.9), rgba(251, 191, 36, 0.3), transparent)',
                    borderRadius: '0.5px',
                    top: '50%',
                    left: '50%',
                    transformOrigin: '50% 100%',
                    transform: `translate(-50%, -100%) rotate(${i * 45 + 22.5}deg) translateY(-6px)`,
                  }}
                  animate={{
                    opacity: [0.5, 0.9, 0.5],
                    scaleY: [0.8, 1.2, 0.8],
                    rotate: [i * 45 + 22.5, i * 45 + 22.5 + 360],
                  }}
                  transition={{
                    opacity: {
                      duration: 1.8,
                      repeat: Infinity,
                      delay: i * 0.15,
                      ease: 'easeInOut',
                    },
                    scaleY: { duration: 1.8, repeat: Infinity, delay: i * 0.15, ease: 'easeInOut' },
                    rotate: { duration: 25, repeat: Infinity, ease: 'linear' },
                  }}
                />
              ))}

              {/* Sun Icon */}
              <motion.div
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.05, 1],
                }}
                transition={{
                  rotate: { duration: 12, repeat: Infinity, ease: 'linear' },
                  scale: { duration: 4, repeat: Infinity, ease: 'easeInOut' },
                }}
                className="relative z-10"
              >
                <Sun
                  size={28}
                  className="text-yellow-500"
                  style={{
                    filter:
                      'drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)) drop-shadow(0 0 16px rgba(251, 191, 36, 0.4))',
                  }}
                />
              </motion.div>

              {/* Dynamic Light Sparkles */}
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={`sparkle-${i}`}
                  className="absolute w-1 h-1 bg-yellow-300 rounded-full"
                  style={{
                    top: i === 0 ? '20%' : i === 1 ? '80%' : i === 2 ? '50%' : '30%',
                    left: i === 0 ? '80%' : i === 1 ? '20%' : i === 2 ? '85%' : '15%',
                  }}
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0, 1.5, 0],
                    rotate: [0, 180, 360],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.5,
                    ease: 'easeInOut',
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.button>
  );
}
