'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { Bell, Moon, Sun, User } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';

import { useTheme } from '@/lib/theme-context';

interface AdminHeaderProps {
  title: string;
  subtitle?: string;
  showSearch?: boolean;
}

export default function AdminHeader({ title, subtitle, showSearch = false }: AdminHeaderProps) {
  const { theme, toggleTheme } = useTheme();
  const [showNotifications, setShowNotifications] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);

  // Close notifications when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
    };

    if (showNotifications) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showNotifications]);

  return (
    <header className="bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark px-6 py-4 backdrop-blur-sm relative z-sticky">
      <div className="flex items-center justify-between">
        {/* Title Section */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex-1"
        >
          <h1 className="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">
            {title}
          </h1>
          {subtitle && (
            <p className="text-sm text-text-secondary-light dark:text-text-secondary-dark mt-1">
              {subtitle}
            </p>
          )}
        </motion.div>

        {/* Actions */}
        <div className="flex items-center space-x-3">

          {/* Theme Toggle */}
          <motion.button
            whileHover={{ scale: 1.05, rotate: 180 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleTheme}
            className="p-3 rounded-xl bg-gray-100 dark:bg-gray-700/50 hover:bg-gray-200 dark:hover:bg-gray-600/50 transition-all duration-200"
            aria-label="Toggle theme"
          >
            <motion.div
              initial={false}
              animate={{ rotate: theme === 'dark' ? 0 : 180 }}
              transition={{ duration: 0.3 }}
            >
              {theme === 'dark' ? (
                <Sun className="w-5 h-5 text-yellow-500" />
              ) : (
                <Moon className="w-5 h-5 text-text-secondary-light" />
              )}
            </motion.div>
          </motion.button>

          {/* Notifications */}
          <div className="relative" ref={notificationRef}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-3 rounded-xl bg-gray-100 dark:bg-gray-700/50 hover:bg-gray-200 dark:hover:bg-gray-600/50 transition-all duration-200 relative group"
              aria-label="Notifications"
            >
              <Bell className="w-5 h-5 text-text-secondary-light dark:text-text-secondary-dark group-hover:text-primary-500 transition-colors" />
              <motion.span
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
              />
            </motion.button>

            {/* Notifications Dropdown */}
            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  className="absolute right-0 mt-2 w-80 bg-surface-light dark:bg-surface-dark rounded-xl border border-border-light dark:border-border-dark shadow-xl z-[99999]"
                >
                  <div className="p-4 border-b border-border-light dark:border-border-dark">
                    <h3 className="font-semibold text-text-primary-light dark:text-text-primary-dark">Notifications</h3>
                  </div>
                  <div className="p-4 text-center">
                    <Bell className="w-8 h-8 text-text-muted-light dark:text-text-muted-dark mx-auto mb-2" />
                    <p className="text-sm text-text-secondary-light dark:text-text-secondary-dark">No new notifications</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </header>
  );
}
