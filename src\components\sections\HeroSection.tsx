'use client';

import { motion } from 'framer-motion';
import { ArrowDown, Download, Code, Terminal } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import Button from '@/components/ui/Button';
// import { personalInfo } from '@/data/portfolio';
import { scrollToElement } from '@/lib/utils';

const typewriterText = [
  'Software Engineer',
  'Frontend Developer',
  'Backend Developer',
  'Full Stack Developer',
  'Problem Solver',
];

export default function HeroSection() {
  const [currentText, setCurrentText] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [personalInfo, setPersonalInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPersonalInfo = async () => {
      try {
        const response = await fetch('/api/portfolio');
        if (response.ok) {
          const data = await response.json();
          setPersonalInfo(data.personal || {});
        }
      } catch (error) {
        console.error('Failed to fetch personal info:', error);
        setPersonalInfo({});
      } finally {
        setLoading(false);
      }
    };

    fetchPersonalInfo();
  }, []);

  useEffect(() => {
    const timeout = setTimeout(
      () => {
        const current = typewriterText[currentText];

        if (isDeleting) {
          setDisplayText(current.substring(0, displayText.length - 1));
        } else {
          setDisplayText(current.substring(0, displayText.length + 1));
        }

        if (!isDeleting && displayText === current) {
          setTimeout(() => setIsDeleting(true), 2000);
        } else if (isDeleting && displayText === '') {
          setIsDeleting(false);
          setCurrentText(prev => (prev + 1) % typewriterText.length);
        }
      },
      isDeleting ? 50 : 100
    );

    return () => clearTimeout(timeout);
  }, [currentText, displayText, isDeleting]);

  const scrollToProjects = () => {
    scrollToElement('featured-projects', 80);
  };

  const socialIcons = {
    // Removed github, linkedin, and mail icons
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 grid-pattern opacity-20 dark:opacity-10" />

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none z-0">
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute top-20 left-10 text-primary-500/20 z-0"
        >
          <Code size={40} />
        </motion.div>

        <motion.div
          animate={{
            y: [0, 15, 0],
            rotate: [0, -5, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 1,
          }}
          className="absolute top-40 right-20 text-primary-500/20 z-0"
        >
          <Terminal size={35} />
        </motion.div>

        <motion.div
          animate={{
            y: [0, -25, 0],
            x: [0, 10, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
          className="absolute bottom-40 left-20 text-neon-green/20"
        >
          <div className="w-3 h-3 bg-current rounded-full" />
        </motion.div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          {/* Name */}
          <motion.h1
            className="text-5xl md:text-7xl lg:text-8xl font-bold font-display"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="gradient-text">
              {loading ? 'Loading...' : personalInfo?.name || 'Your Name'}
            </span>
          </motion.h1>

          {/* Typewriter Title */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="h-16 flex items-center justify-center"
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-medium text-text-secondary-light dark:text-text-secondary-dark">
              <span className="font-mono">{displayText}</span>
              <span className="animate-blink border-r-2 border-primary-500 ml-1" />
            </h2>
          </motion.div>

          {/* Tagline */}
          {!loading && personalInfo?.tagline && (
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-xl md:text-2xl text-text-secondary-light dark:text-text-secondary-dark max-w-3xl mx-auto leading-relaxed"
            >
              {personalInfo.tagline}
            </motion.p>
          )}

          {/* Bio */}
          {!loading && personalInfo?.bio && (
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-lg text-text-secondary-light dark:text-text-secondary-dark max-w-2xl mx-auto leading-relaxed"
            >
              {personalInfo.bio}
            </motion.p>
          )}

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8"
          >
            <Button onClick={scrollToProjects} size="lg" className="min-w-[200px]">
              View My Work
            </Button>

            {!loading && personalInfo?.resumeUrl && (
              <Button
                variant="outline"
                size="lg"
                icon={<Download size={20} />}
                onClick={() => window.open(personalInfo.resumeUrl, '_blank')}
                className="min-w-[200px]"
              >
                Download Resume
              </Button>
            )}
          </motion.div>

          {/* Social Links */}
          {!loading && personalInfo?.socialLinks?.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              className="flex justify-center space-x-6 pt-8 pb-16 md:pb-0"
            >
              {personalInfo.socialLinks.map(
                (
                  social: {
                    icon: string;
                    name: React.Key | null | undefined;
                    url: string | undefined;
                    color: string;
                  },
                  index: number
                ) => {
                  const IconComponent = socialIcons[social.icon as keyof typeof socialIcons];
                  if (!IconComponent) {
                    return null;
                  }

                  return (
                    <motion.a
                      key={social.name}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 1.4 + index * 0.1 }}
                      className="p-3 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-white transition-all duration-200 shadow-lg hover:shadow-xl"
                      style={
                        {
                          '--hover-color': social.color,
                        } as React.CSSProperties
                      }
                      onMouseEnter={e => {
                        e.currentTarget.style.backgroundColor = social.color || '#3B82F6';
                      }}
                      onMouseLeave={e => {
                        e.currentTarget.style.backgroundColor = '';
                      }}
                      aria-label={`Visit ${social.name}`}
                    >
                      {IconComponent && React.createElement(IconComponent, { size: 24 })}
                    </motion.a>
                  );
                }
              )}
            </motion.div>
          )}
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.5 }}
          className="absolute bottom-4 md:bottom-2 left-1/2 transform -translate-x-1/2 z-20"
        >
          <motion.button
            onClick={scrollToProjects}
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="p-3 rounded-full bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm border border-white/20 dark:border-gray-700 hover:bg-white/20 dark:hover:bg-gray-700/50 transition-colors shadow-lg"
            aria-label="Scroll to projects"
          >
            <ArrowDown size={20} className="text-gray-600 dark:text-gray-400" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
