'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { Sun, Moon, Star, Sparkles } from 'lucide-react';
import React, { useEffect, useState, useRef } from 'react';

import { useTheme } from '@/lib/theme-context';
import { cn } from '@/lib/utils';

export default function RealisticSunLight() {
  const { theme, toggleTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const sunRef = useRef<HTMLButtonElement>(null);
  const isDark = theme === 'dark';

  useEffect(() => {
    setMounted(true);
  }, []);

  // Track mouse position for dynamic lighting (optional enhancement)
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    if (!isDark) {
      window.addEventListener('mousemove', handleMouseMove);
    }

    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [isDark]);

  if (!mounted) {return null;}

  return (
    <>
      {/* Sun Toggle Button */}
      <motion.button
        ref={sunRef}
        onClick={toggleTheme}
        className={cn(
          'fixed z-[var(--z-fixed)] p-4',
          'focus:outline-none',
          'group cursor-pointer',
          'top-4 right-4 sm:top-6 sm:right-6 md:top-6 md:right-6',
        )}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
      >
        <div className="relative w-16 h-16 flex items-center justify-center overflow-visible">
          <AnimatePresence mode="wait">
            {isDark ? (
              // Dark Mode - Moon with stars
              <motion.div
                key="dark"
                initial={{ opacity: 0, rotate: -90, scale: 0.5 }}
                animate={{ opacity: 1, rotate: 0, scale: 1 }}
                exit={{ opacity: 0, rotate: 90, scale: 0.5 }}
                transition={{ duration: 0.5, ease: 'easeInOut' }}
                className="relative w-16 h-16 flex items-center justify-center"
              >
                <motion.div
                  animate={{ 
                    y: [0, -3, 0],
                    rotate: [0, 8, 0],
                  }}
                  transition={{ 
                    duration: 5,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                  className="relative z-10"
                >
                  <Moon 
                    size={32} 
                    className="text-blue-400"
                    style={{
                      filter: 'drop-shadow(0 0 12px rgba(96, 165, 250, 0.6))',
                    }}
                  />
                </motion.div>

                {/* Twinkling Stars */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute"
                    style={{
                      top: i === 0 ? '10%' : i === 1 ? '80%' : i === 2 ? '20%' : i === 3 ? '70%' : i === 4 ? '40%' : '60%',
                      left: i === 0 ? '80%' : i === 1 ? '20%' : i === 2 ? '10%' : i === 3 ? '90%' : i === 4 ? '85%' : '15%',
                    }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0.5, 1.2, 0.5],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: i * 0.4,
                      ease: 'easeInOut',
                    }}
                  >
                    <Star size={6} className="text-blue-300" />
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              // Light Mode - Enhanced Realistic Sun
              <motion.div
                key="light"
                initial={{ opacity: 0, rotate: 90, scale: 0.5 }}
                animate={{ opacity: 1, rotate: 0, scale: 1 }}
                exit={{ opacity: 0, rotate: -90, scale: 0.5 }}
                transition={{ duration: 0.5, ease: 'easeInOut' }}
                className="relative w-14 h-14 flex items-center justify-center"
              >
                {/* Outermost Diffuse Glow - Seamless Edge Blending */}
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.15, 0.3, 0.15],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                  className="absolute -inset-4 rounded-full"
                  style={{
                    background: 'radial-gradient(circle, rgba(255, 223, 0, 0.2) 0%, rgba(255, 193, 7, 0.15) 30%, rgba(255, 165, 0, 0.1) 50%, rgba(255, 140, 0, 0.05) 70%, transparent 100%)',
                    filter: 'blur(6px)',
                  }}
                />

                {/* Middle Diffuse Ring */}
                <motion.div
                  animate={{
                    scale: [1, 1.12, 1],
                    opacity: [0.3, 0.5, 0.3],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: 0.3,
                  }}
                  className="absolute -inset-2 rounded-full"
                  style={{
                    background: 'radial-gradient(circle, rgba(255, 215, 0, 0.25) 0%, rgba(255, 193, 7, 0.2) 40%, rgba(255, 152, 0, 0.15) 60%, rgba(255, 140, 0, 0.08) 80%, transparent 100%)',
                    filter: 'blur(4px)',
                  }}
                />

                {/* Inner Soft Glow */}
                <motion.div
                  animate={{
                    scale: [1, 1.06, 1],
                    opacity: [0.5, 0.8, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: 0.6,
                  }}
                  className="absolute inset-1 rounded-full"
                  style={{
                    background: 'radial-gradient(circle, rgba(255, 235, 59, 0.4) 0%, rgba(255, 193, 7, 0.3) 50%, rgba(255, 152, 0, 0.2) 75%, transparent 100%)',
                    filter: 'blur(2px)',
                  }}
                />

                {/* Sun Core - Pure Circle with Gradual Light Falloff */}
                <motion.div
                  animate={{
                    scale: [1, 1.03, 1],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                  className="relative z-20"
                >
                  <div
                    className="w-11 h-11 rounded-full relative"
                    style={{
                      background: `
                        radial-gradient(circle at 30% 30%,
                          rgba(255, 255, 255, 0.95) 0%,
                          rgba(255, 235, 59, 0.9) 10%,
                          rgba(255, 193, 7, 0.8) 25%,
                          rgba(255, 152, 0, 0.7) 45%,
                          rgba(255, 111, 0, 0.5) 65%,
                          rgba(229, 81, 0, 0.3) 85%,
                          transparent 100%
                        )
                      `,
                      boxShadow: `
                        0 0 20px rgba(255, 235, 59, 0.6),
                        0 0 40px rgba(255, 193, 7, 0.5),
                        0 0 60px rgba(255, 152, 0, 0.4),
                        0 0 80px rgba(255, 111, 0, 0.3),
                        0 0 100px rgba(229, 81, 0, 0.2),
                        0 0 120px rgba(229, 81, 0, 0.1)
                      `,
                      filter: 'blur(1px)',
                    }}
                  >
                    {/* Inner bright core - Completely soft edges */}
                    <motion.div
                      animate={{
                        opacity: [0.6, 0.9, 0.6],
                        scale: [1, 1.15, 1],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: 'easeInOut',
                      }}
                      className="absolute top-1.5 left-1.5 w-4 h-4 rounded-full"
                      style={{
                        background: 'radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 235, 59, 0.6) 40%, rgba(255, 193, 7, 0.3) 70%, transparent 100%)',
                        filter: 'blur(2px)',
                      }}
                    />

                    {/* Secondary soft highlight */}
                    <motion.div
                      animate={{
                        opacity: [0.3, 0.6, 0.3],
                        scale: [1, 1.2, 1],
                      }}
                      transition={{
                        duration: 2.5,
                        repeat: Infinity,
                        ease: 'easeInOut',
                        delay: 0.4,
                      }}
                      className="absolute top-2.5 left-2.5 w-2.5 h-2.5 rounded-full"
                      style={{
                        background: 'radial-gradient(circle, rgba(255, 255, 255, 0.5) 0%, rgba(255, 235, 59, 0.3) 50%, transparent 100%)',
                        filter: 'blur(1.5px)',
                      }}
                    />

                    {/* Soft surface texture with seamless blending */}
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background: `
                          radial-gradient(ellipse 70% 50% at 25% 25%,
                            rgba(255, 255, 255, 0.25) 0%,
                            rgba(255, 235, 59, 0.15) 30%,
                            transparent 70%
                          ),
                          radial-gradient(ellipse 50% 70% at 75% 75%,
                            rgba(255, 152, 0, 0.2) 0%,
                            rgba(255, 111, 0, 0.1) 40%,
                            transparent 80%
                          )
                        `,
                        mixBlendMode: 'soft-light',
                        filter: 'blur(0.5px)',
                      }}
                    />
                  </div>
                </motion.div>

                {/* Primary Light Rays - Soft and Gradual */}
                {[...Array(12)].map((_, i) => (
                  <motion.div
                    key={`primary-ray-${i}`}
                    className="absolute"
                    style={{
                      width: '4px',
                      height: '21px',
                      background: `linear-gradient(to top,
                        rgba(255, 193, 7, 0.6) 0%,
                        rgba(255, 193, 7, 0.45) 15%,
                        rgba(255, 152, 0, 0.35) 30%,
                        rgba(255, 235, 59, 0.25) 50%,
                        rgba(255, 235, 59, 0.15) 70%,
                        rgba(255, 235, 59, 0.08) 85%,
                        transparent 100%
                      )`,
                      borderRadius: '2px',
                      top: '50%',
                      left: '50%',
                      transformOrigin: '50% 100%',
                      transform: `translate(-50%, -100%) rotate(${i * 30}deg) translateY(-12px)`,
                      filter: 'blur(1.2px)',
                    }}
                    animate={{
                      opacity: [0.4, 0.8, 0.4],
                      scaleY: [0.7, 1.3, 0.7],
                      rotate: [i * 30, i * 30 + 360],
                    }}
                    transition={{
                      opacity: { duration: 3, repeat: Infinity, delay: i * 0.1, ease: 'easeInOut' },
                      scaleY: { duration: 3, repeat: Infinity, delay: i * 0.1, ease: 'easeInOut' },
                      rotate: { duration: 35, repeat: Infinity, ease: 'linear' },
                    }}
                  />
                ))}

                {/* Secondary Light Rays - Softer and More Diffused */}
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={`secondary-ray-${i}`}
                    className="absolute"
                    style={{
                      width: '3px',
                      height: '15px',
                      background: `linear-gradient(to top,
                        rgba(255, 215, 0, 0.5) 0%,
                        rgba(255, 215, 0, 0.35) 25%,
                        rgba(255, 193, 7, 0.25) 50%,
                        rgba(255, 235, 59, 0.15) 70%,
                        rgba(255, 235, 59, 0.08) 85%,
                        transparent 100%
                      )`,
                      borderRadius: '1.5px',
                      top: '50%',
                      left: '50%',
                      transformOrigin: '50% 100%',
                      transform: `translate(-50%, -100%) rotate(${i * 45 + 22.5}deg) translateY(-9px)`,
                      filter: 'blur(1px)',
                    }}
                    animate={{
                      opacity: [0.2, 0.6, 0.2],
                      scaleY: [0.5, 1.1, 0.5],
                      rotate: [i * 45 + 22.5, i * 45 + 22.5 + 360],
                    }}
                    transition={{
                      opacity: { duration: 2.5, repeat: Infinity, delay: i * 0.15, ease: 'easeInOut' },
                      scaleY: { duration: 2.5, repeat: Infinity, delay: i * 0.15, ease: 'easeInOut' },
                      rotate: { duration: 40, repeat: Infinity, ease: 'linear' },
                    }}
                  />
                ))}

                {/* Sparkle Effects */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={`sparkle-${i}`}
                    className="absolute w-1 h-1 rounded-full"
                    style={{
                      background: 'radial-gradient(circle, #FFEB3B 0%, #FFC107 100%)',
                      top: i === 0 ? '15%' : i === 1 ? '85%' : i === 2 ? '25%' : i === 3 ? '75%' : i === 4 ? '50%' : '40%',
                      left: i === 0 ? '85%' : i === 1 ? '15%' : i === 2 ? '10%' : i === 3 ? '90%' : i === 4 ? '5%' : '95%',
                      boxShadow: '0 0 6px rgba(255, 193, 7, 0.8)',
                    }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1.5, 0],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.3,
                      ease: 'easeInOut',
                    }}
                  />
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.button>

      {/* Radial Light Effect - Only in Light Mode */}
      <AnimatePresence>
        {!isDark && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1, ease: 'easeInOut' }}
            className="fixed inset-0 pointer-events-none z-[2]"
            style={{
              background: `radial-gradient(
                circle 1000px at calc(100% - 80px) 80px,
                rgba(252, 211, 77, 0.12) 0%,
                rgba(245, 158, 11, 0.09) 15%,
                rgba(255, 193, 7, 0.07) 30%,
                rgba(217, 119, 6, 0.05) 50%,
                rgba(252, 211, 77, 0.03) 70%,
                rgba(255, 193, 7, 0.015) 85%,
                transparent 100%
              )`,
              filter: 'blur(1px)',
            }}
          />
        )}
      </AnimatePresence>

      {/* Dynamic Content Illumination */}
      <AnimatePresence>
        {!isDark && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1, ease: 'easeInOut' }}
            className="fixed inset-0 pointer-events-none z-[1]"
          >
            {/* Grid-based illumination zones */}
            <div className="absolute inset-0 grid grid-cols-5 gap-0 w-full h-full">
              {[...Array(5)].map((_, columnIndex) => (
                <motion.div
                  key={columnIndex}
                  className="relative h-full"
                  animate={{
                    opacity: [0.05, 0.15, 0.05],
                  }}
                  transition={{
                    duration: 8 + columnIndex,
                    repeat: Infinity,
                    delay: columnIndex * 0.5,
                    ease: 'easeInOut',
                  }}
                  style={{
                    background: `linear-gradient(
                      135deg,
                      rgba(252, 211, 77, ${0.08 - columnIndex * 0.015}) 0%,
                      rgba(245, 158, 11, ${0.06 - columnIndex * 0.01}) 50%,
                      transparent 100%
                    )`,
                  }}
                />
              ))}
            </div>

            {/* Scrolling light interaction overlay */}
            <motion.div
              className="absolute inset-0"
              animate={{
                background: [
                  'radial-gradient(circle 600px at 85% 15%, rgba(252, 211, 77, 0.1) 0%, transparent 70%)',
                  'radial-gradient(circle 700px at 80% 20%, rgba(245, 158, 11, 0.12) 0%, transparent 70%)',
                  'radial-gradient(circle 600px at 85% 15%, rgba(252, 211, 77, 0.1) 0%, transparent 70%)',
                ],
              }}
              transition={{
                duration: 12,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
